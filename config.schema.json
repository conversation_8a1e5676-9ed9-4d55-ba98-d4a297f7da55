{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"Routes": {"type": "array", "items": {"type": "object", "properties": {"Path": {"type": "string", "pattern": "^/.*$"}, "Method": {"type": "string", "enum": ["GET", "POST", "PUT", "DELETE", "PATCH"]}, "Response": {"type": "object", "properties": {"Status": {"type": "number"}, "Headers": {"type": "object", "additionalProperties": {"type": "string"}}, "Body": {"type": ["string", "object", "array", "null"]}}}}}}}}