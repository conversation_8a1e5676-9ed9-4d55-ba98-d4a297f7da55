type RouteConfig = {
  Path: string;
  Method: string;
  Response: {
    Status?: number;
    Headers?: Record<string, string>;
    Body?: unknown;
  };
};

// Lê o config.json localizado ao lado deste arquivo (ex.: C:\\Sky\\MockHttpServer\\config.json)
const configUrl = new URL('./config.json', import.meta.url);

const config = (await Bun.file(configUrl).json()) as { Routes: RouteConfig[] };

function normalizePath(p: string) {
  return p.startsWith('/') ? p : `/${p}`;
}

// Monta um mapa de rotas chaveado por "METHOD /path"
const routeMap = new Map<string, RouteConfig['Response']>();
for (const r of config.Routes || []) {
  const method = (r.Method || 'GET').toUpperCase();
  const path = normalizePath(r.Path || '/');
  routeMap.set(`${method} ${path}`, r.Response || {});
}

const server = Bun.serve({
  port: 3000,
  fetch(req) {
    const url = new URL(req.url);
    const key = `${req.method.toUpperCase()} ${url.pathname}`;
    const conf = routeMap.get(key);

    if (!conf) {
      return new Response('Not Found', { status: 404 });
    }

    const status = conf.Status ?? 200;
    const headers = new Headers(conf.Headers || {});
    const body = conf.Body;

    if (body === undefined || body === null) {
      return new Response(null, { status, headers });
    }

    if (typeof body === 'string' || body instanceof Uint8Array) {
      return new Response(body, { status, headers });
    }

    if (!headers.has('Content-Type')) {
      headers.set('Content-Type', 'application/json');
    }
    return new Response(JSON.stringify(body), { status, headers });
  },
});

console.log(`Mock server rodando em http://localhost:${server.port}`);
